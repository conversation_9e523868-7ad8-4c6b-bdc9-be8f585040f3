"use client"

import React, { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { X, File, Image, Video, Music, FileText, FileSpreadsheet, FileType } from "lucide-react"

export interface UploadedFile {
  id: string
  file: File
  type: 'image' | 'video' | 'audio' | 'document' | 'other'
  preview?: string
  name: string
  size: number
}

interface MultiFileUploaderProps {
  onFilesChange: (files: UploadedFile[]) => void
  maxFiles?: number
  maxSizePerFile?: number // in MB
  acceptedTypes?: string[]
}

export default function MultiFileUploader({ 
  onFilesChange, 
  maxFiles = 5, 
  maxSizePerFile = 50,
  acceptedTypes = [
    'image/*',
    'video/*', 
    'audio/*',
    'application/pdf',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]
}: MultiFileUploaderProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const getFileType = (file: File): UploadedFile['type'] => {
    if (file.type.startsWith('image/')) return 'image'
    if (file.type.startsWith('video/')) return 'video'
    if (file.type.startsWith('audio/')) return 'audio'
    if (
      file.type === 'application/pdf' ||
      file.type === 'text/plain' ||
      file.type === 'text/csv' ||
      file.type.includes('document') ||
      file.type.includes('spreadsheet') ||
      file.type.includes('excel') ||
      file.type.includes('word')
    ) return 'document'
    return 'other'
  }

  const getFileIcon = (type: UploadedFile['type']) => {
    switch (type) {
      case 'image': return <Image className="h-4 w-4" />
      case 'video': return <Video className="h-4 w-4" />
      case 'audio': return <Music className="h-4 w-4" />
      case 'document': return <FileText className="h-4 w-4" />
      default: return <File className="h-4 w-4" />
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    
    if (files.length === 0) return

    // Check file count limit
    if (uploadedFiles.length + files.length > maxFiles) {
      alert(`Chỉ có thể tải lên tối đa ${maxFiles} file`)
      return
    }

    const newFiles: UploadedFile[] = []

    for (const file of files) {
      // Check file size
      if (file.size > maxSizePerFile * 1024 * 1024) {
        alert(`File "${file.name}" quá lớn. Kích thước tối đa là ${maxSizePerFile}MB`)
        continue
      }

      const fileType = getFileType(file)
      let preview: string | undefined

      // Create preview for images
      if (fileType === 'image') {
        preview = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onload = (e) => resolve(e.target?.result as string)
          reader.readAsDataURL(file)
        })
      }

      const uploadedFile: UploadedFile = {
        id: `${Date.now()}-${Math.random()}`,
        file,
        type: fileType,
        preview,
        name: file.name,
        size: file.size
      }

      newFiles.push(uploadedFile)
    }

    const updatedFiles = [...uploadedFiles, ...newFiles]
    setUploadedFiles(updatedFiles)
    onFilesChange(updatedFiles)

    // Clear input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const removeFile = (fileId: string) => {
    const updatedFiles = uploadedFiles.filter(f => f.id !== fileId)
    setUploadedFiles(updatedFiles)
    onFilesChange(updatedFiles)
  }

  const clearAllFiles = () => {
    setUploadedFiles([])
    onFilesChange([])
  }

  return (
    <div className="space-y-3">
      {/* Upload Button */}
      <div className="flex gap-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => fileInputRef.current?.click()}
          className="flex items-center"
          disabled={uploadedFiles.length >= maxFiles}
        >
          <File className="h-4 w-4 mr-2" />
          Tải file ({uploadedFiles.length}/{maxFiles})
        </Button>
        
        {uploadedFiles.length > 0 && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={clearAllFiles}
            className="text-red-600 hover:text-red-700"
          >
            <X className="h-4 w-4 mr-1" />
            Xóa tất cả
          </Button>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedTypes.join(',')}
        onChange={handleFileChange}
        className="hidden"
      />

      {/* File List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {uploadedFiles.map((file) => (
            <div
              key={file.id}
              className="flex items-center gap-3 p-2 bg-gray-50 rounded-lg border"
            >
              {/* File Icon/Preview */}
              <div className="flex-shrink-0">
                {file.type === 'image' && file.preview ? (
                  <img
                    src={file.preview}
                    alt={file.name}
                    className="w-10 h-10 object-cover rounded"
                  />
                ) : (
                  <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                    {getFileIcon(file.type)}
                  </div>
                )}
              </div>

              {/* File Info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{file.name}</p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(file.size)} • {file.type}
                </p>
              </div>

              {/* Remove Button */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeFile(file.id)}
                className="flex-shrink-0 h-8 w-8 p-0 text-red-600 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* File Type Info */}
      <div className="text-xs text-gray-500">
        <p>Hỗ trợ: Hình ảnh, Video, Audio, PDF, Excel, Word, TXT</p>
        <p>Tối đa {maxFiles} file, mỗi file ≤ {maxSizePerFile}MB</p>
      </div>
    </div>
  )
}
